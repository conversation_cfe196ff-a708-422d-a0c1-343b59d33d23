package ${pkg()};

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;
import lombok.Data;
/**
 * ${formatSemantics(datatype().getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
@Data
public class ${datatype().getName()} implements com.chief.model.api.IDataType {

<#list datatype().fields as field>
    <#if field.getSemantics()?? && field.getSemantics()?has_content>
    /**
     * ${formatSemantics(field.getSemantics(), "    * ")}
     */</#if>
    private ${getJavaTypeName(field.datatype.name)} ${field.name};

</#list>
    public ${getJavaTypeNameDTO(datatype().getName())} toDTO(){
        return new ${getJavaTypeNameDTO(datatype().getName())}(
        <#list datatype().fields as field>
            this.${field.name}<#if !isBasicJavaType(field.datatype.name)>.toDTO()</#if><#if field_has_next>,</#if>
        </#list>
        );
    }
}
