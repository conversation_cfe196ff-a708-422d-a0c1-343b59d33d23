package com.chief.engine.core.model;

import cn.hutool.core.util.ReflectUtil;
import com.chief.engine.core.aware.EventBusModelAware;
import com.chief.engine.core.event.AttributeMsg;
import com.chief.model.api.IDataType;
import com.chief.model.api.ModelContext;
import com.chief.model.api.ModelObject;
import com.chief.model.api.ModelObjectHandler;
import com.chief.model.api.annotation.UpdateType;
import com.chief.model.api.enums.UpdateTypeEnum;
import jakarta.annotation.Nullable;

import java.io.Serializable;
import java.lang.reflect.Field;

public class OperationModel<T extends ModelObject> extends AtomicModel
        implements ModelContext, EventBusModelAware {
    /**
     * 业务模型处理器
     */
    private final ModelObjectHandler<T> handler;

    private final T modelObject;

    private EventBusModel eventBus;

    protected OperationModel(@Nullable AtomicModel parent, String id,
                             ModelObjectHandler handler, T modelObject) {
        this(parent, null, id, handler, modelObject);
    }

    protected OperationModel(@Nullable AtomicModel parent, @Nullable Actor executorParent, String id,
                             ModelObjectHandler handler, T modelObject) {
        super(parent, executorParent, id);
        this.handler = handler;
        this.modelObject = modelObject;
        this.handler.setContext(this);
    }


    @Override
    protected void update(long delta) {
        if (handler != null) {
            handler.update(delta);
        }
    }

    @Override
    public void setEventBusModel(EventBusModel eventBus) {
        this.eventBus = eventBus;
    }

    @Override
    public T getModelObject() {
        return modelObject;
    }

    @Override
    public void subAttribute(Class objectType, String attrName) {

    }


    @Override
    public ModelContext getParentModelContext() {
        if (this.parent instanceof ModelContext parentModelContext) {
            return parentModelContext;
        }
        return null;
    }

    @Override
    protected void onJoinSimulation(long logicTime) {
        handler.onJoinSimulation(logicTime);

        Field[] declaredFields = this.modelObject.getClass().getDeclaredFields();
    }

    private void publishStaticAttribute() {
        Field[] fields = ReflectUtil.getFields(this.modelObject.getClass());
        if (fields != null) {
            for (Field field : fields) {
                UpdateType annotation = field.getAnnotation(UpdateType.class);
                if (annotation != null && UpdateTypeEnum.Static.equals(annotation.value())) {
                    field.setAccessible(true);
                    try {
                        Serializable value = (Serializable) field.get(this.modelObject);
                        if (value != null && value instanceof IDataType dataType) {
                            value = dataType.toDTO();
                        }
                        eventBus.send(new AttributeMsg(getCurrentTime(), getId(), this.getModelObject().getClass(), field.getName(), value));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
