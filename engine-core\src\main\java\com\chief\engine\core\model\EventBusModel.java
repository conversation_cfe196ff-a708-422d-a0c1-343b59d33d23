package com.chief.engine.core.model;

import com.chief.engine.core.event.AttributeMsg;
import com.chief.engine.core.event.SubscribeAttributeMsg;
import com.chief.model.api.ModelObject;
import jakarta.annotation.Nullable;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.chief.engine.core.constant.ID.EVENT_BUS_MODEL_ID;

public class EventBusModel extends AtomicModel {

    protected EventBusModel(@Nullable AtomicModel parent) {
        super(parent, EVENT_BUS_MODEL_ID);
    }

    /**
     * 属性名称-> (类-订阅者)（有序,根据类继承）
     */
    private Map<String, Map<Class<? extends ModelObject>, Set<Actor>>> attrSubscriptions = new HashMap<>();


    @Override
    protected boolean onMessage(Object message) {
        switch (message) {
            case SubscribeAttributeMsg msg -> this.doSubscribe(msg);
            case AttributeMsg msg -> this.publishAttribute(msg);
            default -> super.onMessage(message);
        }
        return true;
    }

    private void publishAttribute(AttributeMsg msg) {

        Set<Actor> subscribers = getSubscribers(msg);

        subscribers.forEach(actor -> actor.send(msg));
    }

    /**
     * 向父级递归 查找订阅者
     * @param msg
     * @return
     */
    private Set<Actor> getSubscribers(AttributeMsg msg){
        Map<Class<? extends ModelObject>, Set<Actor>> actorMap = attrSubscriptions.get(msg.attrName());

        Class<? extends ModelObject> targetClazz = msg.objectType();

        Set<Actor> subscribers = new HashSet<>();

        while (!Object.class.equals(targetClazz)) {
            Set<Actor> actors = actorMap.get(targetClazz);
            if (actors != null) {
                subscribers.addAll(actors);
            }
        }
        return subscribers;
    }

    /**
     * 执行订阅
     *
     * @param msg
     */
    private void doSubscribe(SubscribeAttributeMsg msg) {
        for (String attributeName : msg.attributeNames()) {
            Map<Class<? extends ModelObject>, Set<Actor>> map = attrSubscriptions.computeIfAbsent(attributeName, k -> new HashMap<>());
            map.computeIfAbsent(msg.objectType(), k -> new HashSet<>()).add(msg.subscriber());
        }
    }
}
