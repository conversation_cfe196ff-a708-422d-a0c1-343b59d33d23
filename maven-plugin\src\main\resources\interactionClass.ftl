package ${pkg()};

<#if hasParent()>
import ${getParentClassFullName()};
</#if>
import java.io.Serializable;

/**
 * ${formatSemantics(getSemantics(), " * ")}
 *
 * <AUTHOR>
 * @program: csim
 * @date ${getNow()}
 */
public class ${getClassName()} <#if hasParent()> extends ${getParentClassName()}</#if> {

<#if hasDeclaredParameters()>
    <#list getDeclaredParameters() as parameter>
    <#if parameter.getSemantics()?? && parameter.getSemantics()?has_content>
    /**
     * ${formatSemantics(parameter.getSemantics(), "     * ")}
     */
    </#if>
    private ${getJavaTypeName(parameter.datatype.name)} ${parameter.name};
    </#list>
</#if>
}
